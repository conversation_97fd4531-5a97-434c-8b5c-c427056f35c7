import * as fs from 'fs';
import * as path from 'path';
import { JavaGenerationOptions } from '../types';

/**
 * 文件映射规则接口
 */
interface FileMapping {
  sourcePattern: string;
  targetPath: string;
  condition?: (fileName: string, content: string) => boolean;
}

/**
 * API 端点信息
 */
interface ApiEndpoint {
  path: string;
  method: string;
  parameters: Array<{ name: string; type: string; required: boolean }>;
  responseType: string;
}

/**
 * Spring Boot 项目部署器
 * 负责将生成的 Java 代码复制到 Spring Boot 项目中
 */
export class SpringBootDeployer {
  private springBootProjectPath: string;
  private options: JavaGenerationOptions;
  private fileMappings: FileMapping[];

  constructor(springBootProjectPath: string, options: JavaGenerationOptions) {
    this.springBootProjectPath = springBootProjectPath;
    this.options = options;
    this.fileMappings = this.createDefaultFileMappings();
  }

  /**
   * 创建默认的文件映射规则
   */
  private createDefaultFileMappings(): FileMapping[] {
    return [
      {
        sourcePattern: '*Controller.java',
        targetPath: 'src/main/java/{packagePath}',
        condition: (fileName, content) => content.includes('@RestController') || content.includes('@Controller')
      },
      {
        sourcePattern: '*Service.java',
        targetPath: 'src/main/java/{packagePath}',
        condition: (fileName, content) => content.includes('@Service')
      },
      {
        sourcePattern: 'model/*.java',
        targetPath: 'src/main/java/{packagePath}/model',
        condition: (fileName, content) => !content.includes('@RestController') && !content.includes('@Service')
      },
      {
        sourcePattern: '*.java',
        targetPath: 'src/main/java/{packagePath}',
        condition: () => true // 默认规则，匹配所有其他 Java 文件
      }
    ];
  }

  /**
   * 部署生成的 Java 代码到 Spring Boot 项目
   */
  async deployGeneratedCode(generatedCodePath: string): Promise<void> {
    console.log(`Deploying generated code from ${generatedCodePath} to ${this.springBootProjectPath}`);

    // 验证 Spring Boot 项目路径
    await this.validateSpringBootProject();

    // 复制生成的 Java 文件
    await this.copyJavaFiles(generatedCodePath);

    // 更新 pom.xml 或 build.gradle（如果需要）
    await this.updateBuildConfiguration();

    console.log('Deployment completed successfully');
  }

  /**
   * 验证 Spring Boot 项目结构
   */
  private async validateSpringBootProject(): Promise<void> {
    if (!fs.existsSync(this.springBootProjectPath)) {
      throw new Error(`Spring Boot project path does not exist: ${this.springBootProjectPath}`);
    }

    // 检查是否是 Maven 项目
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    const gradlePath = path.join(this.springBootProjectPath, 'build.gradle');

    if (!fs.existsSync(pomPath) && !fs.existsSync(gradlePath)) {
      throw new Error('Not a valid Spring Boot project: missing pom.xml or build.gradle');
    }

    // 检查 src/main/java 目录
    const srcMainJavaPath = path.join(this.springBootProjectPath, 'src', 'main', 'java');
    if (!fs.existsSync(srcMainJavaPath)) {
      console.log('Creating src/main/java directory...');
      fs.mkdirSync(srcMainJavaPath, { recursive: true });
    }
  }

  /**
   * 复制 Java 文件到 Spring Boot 项目（使用智能映射）
   */
  private async copyJavaFiles(generatedCodePath: string): Promise<void> {
    console.log('Using intelligent file mapping for deployment...');
    await this.copyFilesWithMapping(generatedCodePath);
  }

  /**
   * 使用映射规则复制文件
   */
  private async copyFilesWithMapping(generatedCodePath: string): Promise<void> {
    const packagePath = this.options.packageName.replace(/\./g, '/');

    // 递归查找所有 Java 文件
    const javaFiles = this.findJavaFiles(generatedCodePath);

    for (const javaFile of javaFiles) {
      const relativePath = path.relative(generatedCodePath, javaFile);
      const fileName = path.basename(javaFile);
      const content = fs.readFileSync(javaFile, 'utf-8');

      // 找到匹配的映射规则
      const mapping = this.findMatchingMapping(fileName, relativePath, content);

      if (mapping) {
        const targetPath = mapping.targetPath.replace('{packagePath}', packagePath);
        const fullTargetPath = path.join(this.springBootProjectPath, targetPath);
        const targetFile = path.join(fullTargetPath, fileName);

        // 确保目标目录存在
        fs.mkdirSync(fullTargetPath, { recursive: true });

        console.log(`Mapping ${relativePath} -> ${path.relative(this.springBootProjectPath, targetFile)}`);
        fs.copyFileSync(javaFile, targetFile);
      } else {
        console.warn(`No mapping found for file: ${relativePath}`);
      }
    }
  }

  /**
   * 递归查找所有 Java 文件
   */
  private findJavaFiles(directory: string): string[] {
    const javaFiles: string[] = [];

    if (!fs.existsSync(directory)) {
      return javaFiles;
    }

    const items = fs.readdirSync(directory);

    for (const item of items) {
      const fullPath = path.join(directory, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        javaFiles.push(...this.findJavaFiles(fullPath));
      } else if (stat.isFile() && item.endsWith('.java')) {
        javaFiles.push(fullPath);
      }
    }

    return javaFiles;
  }

  /**
   * 查找匹配的映射规则
   */
  private findMatchingMapping(fileName: string, relativePath: string, content: string): FileMapping | null {
    for (const mapping of this.fileMappings) {
      if (this.matchesPattern(fileName, relativePath, mapping.sourcePattern)) {
        if (!mapping.condition || mapping.condition(fileName, content)) {
          return mapping;
        }
      }
    }
    return null;
  }

  /**
   * 检查文件是否匹配模式
   */
  private matchesPattern(fileName: string, relativePath: string, pattern: string): boolean {
    // 简单的模式匹配实现
    if (pattern === '*.java') {
      return fileName.endsWith('.java');
    }

    if (pattern.startsWith('*') && pattern.endsWith('.java')) {
      const suffix = pattern.substring(1);
      return fileName.endsWith(suffix);
    }

    if (pattern.startsWith('model/')) {
      return relativePath.includes('model/') || relativePath.includes('model\\');
    }

    return fileName === pattern;
  }

  /**
   * 递归复制目录
   */
  private async copyDirectoryRecursive(source: string, destination: string): Promise<void> {
    if (!fs.existsSync(source)) {
      console.warn(`Source directory does not exist: ${source}`);
      return;
    }

    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);

    for (const item of items) {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      const stat = fs.statSync(sourcePath);

      if (stat.isDirectory()) {
        await this.copyDirectoryRecursive(sourcePath, destPath);
      } else if (stat.isFile() && item.endsWith('.java')) {
        console.log(`Copying ${sourcePath} to ${destPath}`);
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  }

  /**
   * 更新构建配置（添加必要的依赖）
   */
  private async updateBuildConfiguration(): Promise<void> {
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    
    if (fs.existsSync(pomPath)) {
      await this.updatePomXml(pomPath);
    }
  }

  /**
   * 更新 pom.xml 文件，添加必要的依赖
   */
  private async updatePomXml(pomPath: string): Promise<void> {
    const pomContent = fs.readFileSync(pomPath, 'utf-8');
    
    // 检查是否已经包含必要的依赖
    const requiredDependencies = [
      'spring-boot-starter-web',
      'spring-boot-starter-validation',
      'jackson-annotations'
    ];

    let needsUpdate = false;
    const missingDependencies: string[] = [];

    for (const dep of requiredDependencies) {
      if (!pomContent.includes(dep)) {
        missingDependencies.push(dep);
        needsUpdate = true;
      }
    }

    if (needsUpdate) {
      console.log('Adding missing dependencies to pom.xml:', missingDependencies);
      await this.addDependenciesToPom(pomPath, pomContent, missingDependencies);
    } else {
      console.log('All required dependencies are already present in pom.xml');
    }
  }

  /**
   * 添加依赖到 pom.xml
   */
  private async addDependenciesToPom(pomPath: string, pomContent: string, dependencies: string[]): Promise<void> {
    let updatedContent = pomContent;

    // 查找 dependencies 标签
    const dependenciesRegex = /(<dependencies>)([\s\S]*?)(<\/dependencies>)/;
    const match = updatedContent.match(dependenciesRegex);

    if (match) {
      const dependenciesSection = match[2];
      let newDependencies = '';

      for (const dep of dependencies) {
        switch (dep) {
          case 'spring-boot-starter-web':
            if (!dependenciesSection.includes('spring-boot-starter-web')) {
              newDependencies += `
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>`;
            }
            break;
          case 'spring-boot-starter-validation':
            if (!dependenciesSection.includes('spring-boot-starter-validation')) {
              newDependencies += `
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>`;
            }
            break;
          case 'jackson-annotations':
            if (!dependenciesSection.includes('jackson-annotations')) {
              newDependencies += `
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>`;
            }
            break;
        }
      }

      if (newDependencies) {
        updatedContent = updatedContent.replace(
          dependenciesRegex,
          `$1$2${newDependencies}
    $3`
        );

        // 备份原文件
        fs.copyFileSync(pomPath, `${pomPath}.backup`);
        
        // 写入更新的内容
        fs.writeFileSync(pomPath, updatedContent, 'utf-8');
        console.log('Updated pom.xml with new dependencies');
      }
    } else {
      console.warn('Could not find dependencies section in pom.xml');
    }
  }

  /**
   * 验证部署是否成功
   */
  async validateDeployment(): Promise<boolean> {
    try {
      const srcMainJavaPath = path.join(this.springBootProjectPath, 'src', 'main', 'java');
      const packagePath = path.join(srcMainJavaPath, ...this.options.packageName.split('.'));
      
      // 检查是否有 Java 文件被复制
      if (fs.existsSync(packagePath)) {
        const files = fs.readdirSync(packagePath, { recursive: true });
        const javaFiles = files.filter(file => typeof file === 'string' && file.endsWith('.java'));
        
        if (javaFiles.length > 0) {
          console.log(`Deployment validation successful: ${javaFiles.length} Java files found`);
          return true;
        }
      }
      
      console.warn('Deployment validation failed: No Java files found in target directory');
      return false;
    } catch (error) {
      console.error('Error during deployment validation:', error);
      return false;
    }
  }

  /**
   * 获取 Spring Boot 项目信息
   */
  getProjectInfo(): { path: string; type: 'maven' | 'gradle' | 'unknown' } {
    const pomPath = path.join(this.springBootProjectPath, 'pom.xml');
    const gradlePath = path.join(this.springBootProjectPath, 'build.gradle');

    let type: 'maven' | 'gradle' | 'unknown' = 'unknown';
    if (fs.existsSync(pomPath)) {
      type = 'maven';
    } else if (fs.existsSync(gradlePath)) {
      type = 'gradle';
    }

    return {
      path: this.springBootProjectPath,
      type
    };
  }

  /**
   * 验证生成的 API 是否与 TIBCO BW swagger.json 一致
   */
  async validateApiConsistency(swaggerJsonPath: string): Promise<{ isConsistent: boolean; differences: string[] }> {
    const differences: string[] = [];

    try {
      // 读取 swagger.json
      const swaggerContent = fs.readFileSync(swaggerJsonPath, 'utf-8');
      const swaggerSpec = JSON.parse(swaggerContent);

      // 提取预期的 API 端点
      const expectedEndpoints = this.extractEndpointsFromSwagger(swaggerSpec);

      // 分析生成的控制器代码
      const actualEndpoints = await this.extractEndpointsFromControllers();

      // 比较端点
      const comparison = this.compareEndpoints(expectedEndpoints, actualEndpoints);

      return {
        isConsistent: comparison.differences.length === 0,
        differences: comparison.differences
      };

    } catch (error) {
      differences.push(`Error validating API consistency: ${error}`);
      return { isConsistent: false, differences };
    }
  }

  /**
   * 从 swagger.json 提取 API 端点信息
   */
  private extractEndpointsFromSwagger(swaggerSpec: any): ApiEndpoint[] {
    const endpoints: ApiEndpoint[] = [];

    if (swaggerSpec.paths) {
      for (const [pathPattern, pathItem] of Object.entries(swaggerSpec.paths)) {
        for (const [method, operation] of Object.entries(pathItem as any)) {
          if (typeof operation === 'object' && operation !== null) {
            const parameters = (operation as any).parameters || [];
            const responses = (operation as any).responses || {};

            // 获取成功响应的类型
            let responseType = 'String';
            const successResponse = responses['200'] || responses['201'];
            if (successResponse && successResponse.schema && successResponse.schema.$ref) {
              responseType = successResponse.schema.$ref.split('/').pop() || 'String';
            }

            endpoints.push({
              path: pathPattern,
              method: method.toUpperCase(),
              parameters: parameters.map((param: any) => ({
                name: param.name,
                type: param.type || 'string',
                required: param.required || false
              })),
              responseType
            });
          }
        }
      }
    }

    return endpoints;
  }

  /**
   * 从生成的控制器代码中提取 API 端点信息
   */
  private async extractEndpointsFromControllers(): Promise<ApiEndpoint[]> {
    const endpoints: ApiEndpoint[] = [];
    const packagePath = this.options.packageName.replace(/\./g, '/');
    const controllersPath = path.join(this.springBootProjectPath, 'src', 'main', 'java', packagePath);

    if (!fs.existsSync(controllersPath)) {
      return endpoints;
    }

    const javaFiles = this.findJavaFiles(controllersPath);
    const controllerFiles = javaFiles.filter(file =>
      file.endsWith('Controller.java') &&
      fs.readFileSync(file, 'utf-8').includes('@RestController')
    );

    for (const controllerFile of controllerFiles) {
      const content = fs.readFileSync(controllerFile, 'utf-8');
      const controllerEndpoints = this.parseControllerEndpoints(content);
      endpoints.push(...controllerEndpoints);
    }

    return endpoints;
  }

  /**
   * 解析控制器文件中的端点信息
   */
  private parseControllerEndpoints(content: string): ApiEndpoint[] {
    const endpoints: ApiEndpoint[] = [];

    // 分步解析：先找到所有的映射注解，然后找对应的方法
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 查找映射注解
      const mappingMatch = line.match(/@(GetMapping|PostMapping|PutMapping|DeleteMapping|RequestMapping)\s*\(\s*["']([^"']*?)["']\s*\)/);

      if (mappingMatch) {
        const annotation = mappingMatch[1];
        const path = mappingMatch[2];

        let method = 'GET';
        if (annotation === 'PostMapping') method = 'POST';
        else if (annotation === 'PutMapping') method = 'PUT';
        else if (annotation === 'DeleteMapping') method = 'DELETE';

        // 查找下一行的方法定义
        let methodLine = '';
        let responseType = 'String';
        let parameters: Array<{ name: string; type: string; required: boolean }> = [];

        for (let j = i + 1; j < lines.length && j < i + 5; j++) {
          const nextLine = lines[j].trim();
          if (nextLine.includes('public') && nextLine.includes('ResponseEntity')) {
            methodLine = nextLine;

            // 提取返回类型
            const returnTypeMatch = nextLine.match(/ResponseEntity<(\w+)>/);
            if (returnTypeMatch) {
              responseType = returnTypeMatch[1];
            }

            // 提取参数
            const paramMatch = nextLine.match(/\(([^)]*)\)/);
            if (paramMatch) {
              const paramString = paramMatch[1];

              // 查找 @RequestParam 参数
              const requestParamRegex = /@RequestParam\s*\(\s*["']([^"']*?)["']\s*\)\s*\w+\s+(\w+)/g;
              let paramResult;
              while ((paramResult = requestParamRegex.exec(paramString)) !== null) {
                parameters.push({
                  name: paramResult[1],
                  type: 'string',
                  required: true
                });
              }

              // 也处理简单的 @RequestParam 参数
              const simpleParamRegex = /@RequestParam\s+\w+\s+(\w+)/g;
              let simpleResult;
              while ((simpleResult = simpleParamRegex.exec(paramString)) !== null) {
                const paramName = simpleResult[1];
                if (!parameters.find(p => p.name === paramName)) {
                  parameters.push({
                    name: paramName,
                    type: 'string',
                    required: true
                  });
                }
              }
            }
            break;
          }
        }

        endpoints.push({
          path,
          method,
          parameters,
          responseType
        });
      }
    }

    return endpoints;
  }

  /**
   * 比较预期和实际的 API 端点
   */
  private compareEndpoints(expected: ApiEndpoint[], actual: ApiEndpoint[]): { differences: string[] } {
    const differences: string[] = [];

    // 检查每个预期的端点
    for (const expectedEndpoint of expected) {
      const matchingActual = actual.find(a =>
        a.path === expectedEndpoint.path && a.method === expectedEndpoint.method
      );

      if (!matchingActual) {
        differences.push(`Missing endpoint: ${expectedEndpoint.method} ${expectedEndpoint.path}`);
      } else {
        // 检查参数
        for (const expectedParam of expectedEndpoint.parameters) {
          const matchingParam = matchingActual.parameters.find(p => p.name === expectedParam.name);
          if (!matchingParam) {
            differences.push(`Missing parameter '${expectedParam.name}' in ${expectedEndpoint.method} ${expectedEndpoint.path}`);
          }
        }

        // 检查返回类型
        if (expectedEndpoint.responseType !== matchingActual.responseType) {
          differences.push(`Response type mismatch for ${expectedEndpoint.method} ${expectedEndpoint.path}: expected ${expectedEndpoint.responseType}, got ${matchingActual.responseType}`);
        }
      }
    }

    // 检查是否有额外的端点
    for (const actualEndpoint of actual) {
      const matchingExpected = expected.find(e =>
        e.path === actualEndpoint.path && e.method === actualEndpoint.method
      );

      if (!matchingExpected) {
        differences.push(`Unexpected endpoint: ${actualEndpoint.method} ${actualEndpoint.path}`);
      }
    }

    return { differences };
  }

  /**
   * 添加自定义文件映射规则
   */
  addFileMapping(mapping: FileMapping): void {
    this.fileMappings.unshift(mapping); // 添加到开头，优先级更高
  }

  /**
   * 启动 Spring Boot 应用进行测试
   */
  async startSpringBootForTesting(): Promise<{ success: boolean; port: number; error?: string }> {
    try {
      const projectInfo = this.getProjectInfo();

      if (projectInfo.type === 'maven') {
        // 这里可以添加启动 Maven Spring Boot 应用的逻辑
        // 目前返回模拟结果
        return { success: true, port: 8080 };
      } else if (projectInfo.type === 'gradle') {
        // 这里可以添加启动 Gradle Spring Boot 应用的逻辑
        return { success: true, port: 8080 };
      } else {
        return { success: false, port: 0, error: 'Unknown project type' };
      }
    } catch (error) {
      return { success: false, port: 0, error: `Failed to start application: ${error}` };
    }
  }
}
