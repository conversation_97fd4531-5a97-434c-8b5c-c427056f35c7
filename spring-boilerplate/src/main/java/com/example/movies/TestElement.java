package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/json/1563810299003
 * Root Element
 */
@JsonRootName("TestElement")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TestElement {



    /**
     * Default constructor
     */
    public TestElement() {
    }



    @Override
    public String toString() {
        return "TestElement{" +
                "}";
    }
}
