package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("HttpResponseHeaders")
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpResponseHeaders {



    /**
     * Default constructor
     */
    public HttpResponseHeaders() {
    }



    @Override
    public String toString() {
        return "HttpResponseHeaders{" +
                "}";
    }
}
