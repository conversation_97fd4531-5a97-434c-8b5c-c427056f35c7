package com.example.movies.model;


/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Complex Type
 */
public class RatingsType {

    private String source;

    private String value;

    /**
     * Default constructor
     */
    public RatingsType() {
    }

    /**
     * Parameterized constructor
     */
    public RatingsType(String source, String value) {
        this.source = source;
        this.value = value;
    }

    /**
     * Get source
     */
    public String getSource() {
        return source;
    }

    /**
     * Set source
     */
    public void setSource(String source) {
        this.source = source;
    }


    /**
     * Get value
     */
    public String getValue() {
        return value;
    }

    /**
     * Set value
     */
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "RatingsType{" +
                "source=" + source + ", " + "value=" + value +
                "}";
    }
}
