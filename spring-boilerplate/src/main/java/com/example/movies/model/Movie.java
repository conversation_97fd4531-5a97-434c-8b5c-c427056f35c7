package com.example.movies.model;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Root Element
 */
public class Movie {

    private List<MoviesType> movies = new ArrayList<>();

    /**
     * Default constructor
     */
    public Movie() {
    }

    /**
     * Parameterized constructor
     */
    public Movie(List<MoviesType> movies) {
        this.movies = movies;
    }

    /**
     * Get movies
     */
    public List<MoviesType> getMovies() {
        return movies;
    }

    /**
     * Set movies
     */
    public void setMovies(List<MoviesType> movies) {
        this.movies = movies;
    }

    @Override
    public String toString() {
        return "Movie{" +
                "movies=" + movies +
                "}";
    }
}
