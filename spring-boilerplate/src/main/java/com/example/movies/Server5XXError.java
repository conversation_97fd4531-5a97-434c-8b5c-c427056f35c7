package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("Server5XXError")
@JsonIgnoreProperties(ignoreUnknown = true)
public class Server5XXError {



    /**
     * Default constructor
     */
    public Server5XXError() {
    }



    @Override
    public String toString() {
        return "Server5XXError{" +
                "}";
    }
}
